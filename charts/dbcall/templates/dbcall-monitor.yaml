{{- $global_data := .Values.global }}
{{- if $global_data.serviceMonitor -}}
{{- if $global_data.serviceMonitor.enabled -}}
apiVersion: {{ include "common.capabilities.ServiceMonitor.apiVersion" . }}
kind: ServiceMonitor
metadata:
  name: {{ .Values.appName }}
  labels:
    {{- include "dbcall.labels" . | nindent 4 }}
    {{- include "common.ServiceMonitor.metadata.labes" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "dbcall.labels" . | nindent 6 }}
  endpoints:
  - path: {{ .Values.prometheusPath | default "/metrics" }}
    targetPort: {{ .Values.metricsPort }}
    interval: {{ .Values.prometheusInterval | default "15s" }}
{{- end -}}
{{- end -}}
