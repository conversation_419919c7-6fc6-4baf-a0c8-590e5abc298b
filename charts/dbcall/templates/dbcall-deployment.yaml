{{- $global_data := .Values.global }}
{{- $corezoid_data := default $global_data.corezoid $global_data }}
{{- $dbcall_data := $corezoid_data.dbcall }}

apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ .Values.appName }}-deployment
  labels:
    {{- include "dbcall.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "dbcall.selectorLabels" . | nindent 6 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  {{- if $dbcall_data.corezoid_dbcall.minReplicas }}
  replicas: {{ $dbcall_data.corezoid_dbcall.minReplicas }}
  {{- else }}
  replicas: {{ .Values.scale.minReplicas }}
  {{- end }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/dbcall-configmap.yaml") . | sha256sum }}
      labels:
        {{- include "dbcall.labels" . | nindent 8 }}
    spec:
      {{- if $global_data.imagePullSecrets }}
      imagePullSecrets:
      {{- range $pullSecret := $global_data.imagePullSecrets }}
        - name: {{ $pullSecret }}
      {{- end }}
      {{- end }}
      terminationGracePeriodSeconds: 40
      initContainers:
        {{ include "CorezoidInitWaitRabbitMQResolve" . | nindent 8 | trim }}
        {{ include "CorezoidInitWaitRabbitMQPort" . | nindent 8 | trim }}
      containers:
        - name: {{ .Values.appName }}
          imagePullPolicy: {{ $global_data.imagePullPolicy }}
          image: {{ include "dbcall.imageUrl" . | quote }}
          env:
          - name: APP_ENV
            value: production
          - name: RABBIT_HOST
            valueFrom:
              secretKeyRef:
                name: {{ include "dbcall.rabbitSecretName" . }}
                key: host
          - name: RABBIT_PORT
            valueFrom:
              secretKeyRef:
                name: {{ include "dbcall.rabbitSecretName" . }}
                key: port
          - name: RABBIT_VHOST
            valueFrom:
              secretKeyRef:
                name: {{ include "dbcall.rabbitSecretName" . }}
                key: vhost
          - name: RABBIT_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ include "dbcall.rabbitSecretName" . }}
                key: username
          - name: RABBIT_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ include "dbcall.rabbitSecretName" . }}
                key: password
          command: ["./dbcall"]
          args: ["--config=/go/bin/config/production.json"]
          ports:
            - name: application
              containerPort: {{ .Values.appPort }}
              protocol: TCP
            - name: monitoring
              containerPort: {{ .Values.metricsPort }}
              protocol: TCP
          volumeMounts:
            - name: {{ .Values.appName }}-config
              mountPath: /go/bin/config/production.json
              subPath: production.json
          {{- include "dbcall.corezoid_dbcall.resources" . | indent 10 }}
          readinessProbe:
            httpGet:
              port: {{ .Values.readinessPort }}
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            httpGet:
              port: {{ .Values.livenessPort }}
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          securityContext:
            runAsUser: 501
            runAsGroup: 501
            runAsNonRoot: true
            readOnlyRootFilesystem: true
            allowPrivilegeEscalation: false
            privileged: false
            capabilities:
              drop:
                - NET_ADMIN
                - SYS_ADMIN
                - MAC_ADMIN
                - SETUID
                - SETGID
                - FOWNER
                - CHOWN
                - MKNOD
      volumes:
        - name: {{ .Values.appName }}-config
          configMap:
            name: {{ .Values.appName }}-config
      {{- with $dbcall_data.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $dbcall_data.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
