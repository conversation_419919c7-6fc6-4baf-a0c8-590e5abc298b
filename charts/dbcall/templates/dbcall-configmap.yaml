{{- $global_data := .Values.global }}
{{- $corezoid_data := default $global_data.corezoid $global_data }}
{{- $dbcall_data := $corezoid_data.dbcall }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.appName }}-config
  labels:
    {{- include "dbcall.labels" . | nindent 4 }}
data:
  production.json: |
    {
      "log_level": "{{ $dbcall_data.log_level | default "info" }}",
      "liveness_port": {{ .Values.livenessPort }},
      "readiness_port": {{ .Values.readinessPort }},
      "metrics": {
        "listen_port": {{ .Values.metricsPort }}
      },
      "query_consumer": {
        "host": "${RABBIT_HOST}",
        "port": ${RABBIT_PORT},
        "username": "${RABBIT_USERNAME}",
        "password": "${RABBIT_PASSWORD}",
        "vhost": "${RABBIT_VHOST}",
        "prefetch": {{ $dbcall_data.prefetch | default 100 }},
        "queue_name": "dbcall-query-1",
        "ssl": {
          "enabled": false,
          "insecure_skip_verify": false
        }
      },
      "query_reply_publisher": {
        "host": "${RABBIT_HOST}",
        "port": ${RABBIT_PORT},
        "username": "${RABBIT_USERNAME}",
        "password": "${RABBIT_PASSWORD}",
        "vhost": "${RABBIT_VHOST}",
        "ssl": {
          "enabled": false,
          "insecure_skip_verify": false
        }
      }
    }
