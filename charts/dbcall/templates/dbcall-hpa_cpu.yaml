{{- $global_data := .Values.global }}
{{- $data := $global_data.dbcall.corezoid_dbcall }}
{{- if $data.hpa.enabled }}
apiVersion: {{ include "common.capabilities.hpa.apiVersion" ( dict "context" $ ) }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ .Values.appName }}-cpu-autoscale
  labels:
    {{- include "dbcall.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
    kind: Deployment
    name: {{ .Values.appName }}-deployment
  minReplicas: {{ default (default 2 .Values.scale.minReplicas) $data.minReplicas }}
  maxReplicas: {{ default (default 10 .Values.scale.maxReplicas) $data.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        {{- if semverCompare "<1.23-0" (include "common.capabilities.kubeVersion" .) }}
        targetAverageUtilization: {{ .Values.scale.targetCPUUtilizationPercentage }}
        {{- else }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.scale.targetCPUUtilizationPercentage }}
        {{- end }}
{{- end -}}
