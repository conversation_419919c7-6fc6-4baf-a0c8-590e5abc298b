global:
  repotype: "public"
  product: corezoid
  serviceMonitor:
    enabled: true
  # imagePullSecrets:
  #   name: corezoid-secret-dbcall-new-prod
  imagePullPolicy: IfNotPresent
  imageInit:
    repository: hub.corezoid.com/hub.docker.com/library/alpine
    pullPolicy: IfNotPresent
    tag: "3.20"
  #######  RabbitMQ  ########
  ## Supported version  3.9.*
  mq:
    # if internal true - create and use internal rmq container
    # if internal false - enable external rmq (on aws ec2 instances as example)
    internal: true
    ## secret configuration for rabbitmq
    secret:
      ## true - secret will be created automatically with provided values
      ## false - secret should be created manually
      create: true
      name: "rabbitmq-secret"
      data:
        # host: "rabbitmq.rabbitmq.svc.cluster.local"
        host: "RABBTMQ_HOST"  # If internal: true, then this host
        port: "RABBTMQ_PORT"
        vhost: "RABBTMQ_VHOST"
        username: "RABBTMQ_USER"
        password: "RABBTMQ_PASSWORD"
    # vm_memory_high_watermark.relative
    memoryHighWatermark: 0.3
  dbcall:
    corezoid_dbcall:
      tag: "2.2.0"
      minReplicas: 3
      maxReplicas: 10
      hpa:
        enabled: true
      resources:
        limits:
          memory: 500Mi
        requests:
          cpu: 100m
          memory: 200Mi
      secret:
        postgres:
          annotations: {}
      affinity: {}
