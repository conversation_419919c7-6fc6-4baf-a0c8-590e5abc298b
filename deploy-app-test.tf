# Configure Kubernetes and Helm providers to use the EKS cluster
# provider "kubernetes" {
#   host                   = data.aws_eks_cluster.cluster.endpoint
#   cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
#   token                  = data.aws_eks_cluster_auth.cluster.token
# }
#
# provider "helm" {
#   kubernetes {
#     host                   = data.aws_eks_cluster.cluster.endpoint
#     cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
#     token                  = data.aws_eks_cluster_auth.cluster.token
#   }
# }

# Create namespace for dbcall application
resource "kubernetes_namespace" "dbcall_test" {
  metadata {
    name = "dbcall-test"
    labels = {
      name        = "dbcall-test"
      environment = var.environment
      managed-by  = "terraform"
    }
  }

  depends_on = [module.eks]
}

# Deploy dbcall application using local Helm chart
resource "helm_release" "dbcall" {
  name       = "dbcall"
  namespace  = kubernetes_namespace.dbcall_test.metadata[0].name
  chart      = "${path.module}/charts/dbcall"  # Using local chart

  # Use the values from values-test.yaml file
  values = [
    file("${path.module}/values-test.yaml")
  ]

  # Additional configuration
  timeout         = 600
  cleanup_on_fail = true
  wait            = true

  depends_on = [
    kubernetes_namespace.dbcall_test,
    module.eks
  ]
}

# Output the namespace name
output "dbcall_namespace" {
  description = "The namespace where dbcall is deployed"
  value       = kubernetes_namespace.dbcall_test.metadata[0].name
}

# Output the helm release status
output "dbcall_release_status" {
  description = "The status of the dbcall helm release"
  value       = helm_release.dbcall.status
}

# Output the helm release version
output "dbcall_release_version" {
  description = "The version of the dbcall helm release"
  value       = helm_release.dbcall.version
}
